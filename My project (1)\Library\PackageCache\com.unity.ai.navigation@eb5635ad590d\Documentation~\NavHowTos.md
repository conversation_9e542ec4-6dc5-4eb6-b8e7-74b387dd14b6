# Navigation How-Tos

The following topics describe a set of techniques, and include code samples, to implement common tasks in navigation. As with all code in our documentation, you are free to use these samples for any purpose without crediting <PERSON>.

| **Topic**  | **Description**                |
|:-----------|:-------------------------------|
| [Tell a NavMeshAgent to Move to a Destination](./NavMoveToDestination.md) | Set a destination for a NavMesh agent. |
| [Move an Agent to a Position Clicked by the Mouse](./NavMoveToClickPoint.md) | Use a mouse click to set the destination for a NavMesh agent. |
| [Make an Agent Patrol Between a Set of Points](./NavAgentPatrol.md) | Set patrol points for a NavMesh agent. |
| [Couple Animation and Navigation](./CouplingAnimationAndNavigation.md) | Integrate animation into your navigation. |
