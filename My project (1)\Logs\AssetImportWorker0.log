Using pre-set license
Built from '6000.0/release' branch; Version is '6000.0.45f1 (d91bd3d4e081) revision 14228435'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 15790 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Users\<USER>\Documents\6000.0.45f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
C:/Users/<USER>/My project (1)
-logFile
Logs/AssetImportWorker0.log
-srvPort
50183
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: C:/Users/<USER>/My project (1)
C:/Users/<USER>/My project (1)
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [22336]  Target information:

Player connection [22336]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 20966647 [EditorId] 20966647 [Version] 1048832 [Id] WindowsEditor(7,V) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [22336] Host joined multi-casting on [***********:54997]...
Player connection [22336] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 145.49 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.45f1 (d91bd3d4e081)
[Subsystems] Discovering subsystems at path C:/Users/<USER>/Documents/6000.0.45f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/My project (1)/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 3050 Laptop GPU (ID=0x25a2)
    Vendor:   NVIDIA
    VRAM:     3964 MB
    Driver:   32.0.15.7640
Initialize mono
Mono path[0] = 'C:/Users/<USER>/Documents/6000.0.45f1/Editor/Data/Managed'
Mono path[1] = 'C:/Users/<USER>/Documents/6000.0.45f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Users/<USER>/Documents/6000.0.45f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56196
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Users/<USER>/Documents/6000.0.45f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.028272 seconds.
- Loaded All Assemblies, in 11.077 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.430 seconds
Domain Reload Profiling: 11504ms
	BeginReloadAssembly (9571ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (313ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (83ms)
	LoadAllAssembliesAndSetupDomain (1095ms)
		LoadAssemblies (9567ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1092ms)
			TypeCache.Refresh (1091ms)
				TypeCache.ScanAssembly (800ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (430ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (378ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (29ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (63ms)
			ProcessInitializeOnLoadAttributes (196ms)
			ProcessInitializeOnLoadMethodAttributes (85ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  3.296 seconds
Refreshing native plugins compatible for Editor in 1.81 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.687 seconds
Domain Reload Profiling: 4978ms
	BeginReloadAssembly (180ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (34ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (44ms)
	LoadAllAssembliesAndSetupDomain (3014ms)
		LoadAssemblies (2767ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (350ms)
			TypeCache.Refresh (280ms)
				TypeCache.ScanAssembly (206ms)
			BuildScriptInfoCaches (54ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (1687ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1001ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (127ms)
			ProcessInitializeOnLoadAttributes (396ms)
			ProcessInitializeOnLoadMethodAttributes (462ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 2.51 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 212 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5975 unused Assets / (7.2 MB). Loaded Objects now: 6638.
Memory consumption went from 233.3 MB to 226.1 MB.
Total: 15.772300 ms (FindLiveObjects: 1.005800 ms CreateObjectMapping: 1.243400 ms MarkObjects: 6.345600 ms  DeleteObjects: 7.175500 ms)

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.940 seconds
Refreshing native plugins compatible for Editor in 1.58 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.004 seconds
Domain Reload Profiling: 1943ms
	BeginReloadAssembly (251ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (68ms)
	RebuildCommonClasses (44ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (40ms)
	LoadAllAssembliesAndSetupDomain (588ms)
		LoadAssemblies (481ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (229ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (197ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1004ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (671ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (139ms)
			ProcessInitializeOnLoadAttributes (441ms)
			ProcessInitializeOnLoadMethodAttributes (72ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 4.34 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 45 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5972 unused Assets / (7.9 MB). Loaded Objects now: 6654.
Memory consumption went from 218.8 MB to 210.9 MB.
Total: 33.889700 ms (FindLiveObjects: 2.968500 ms CreateObjectMapping: 3.238300 ms MarkObjects: 14.330100 ms  DeleteObjects: 13.348000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1368.336506 seconds.
  path: Assets/Enemy.prefab
  artifactKey: Guid(6349f852420cc744b9790061728ac5c2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Enemy.prefab using Guid(6349f852420cc744b9790061728ac5c2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f4b509974c444747884e141289d62a0d') in 0.8515271 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 38

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.837 seconds
Refreshing native plugins compatible for Editor in 1.59 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.813 seconds
Domain Reload Profiling: 1648ms
	BeginReloadAssembly (245ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (24ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (69ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (507ms)
		LoadAssemblies (409ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (202ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (178ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (813ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (598ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (119ms)
			ProcessInitializeOnLoadAttributes (387ms)
			ProcessInitializeOnLoadMethodAttributes (76ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 3.40 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5973 unused Assets / (7.8 MB). Loaded Objects now: 6691.
Memory consumption went from 230.5 MB to 222.7 MB.
Total: 33.696600 ms (FindLiveObjects: 2.799500 ms CreateObjectMapping: 3.293400 ms MarkObjects: 13.927900 ms  DeleteObjects: 13.670700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.084 seconds
Refreshing native plugins compatible for Editor in 1.50 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.855 seconds
Domain Reload Profiling: 1936ms
	BeginReloadAssembly (232ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (58ms)
	RebuildCommonClasses (69ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (61ms)
	LoadAllAssembliesAndSetupDomain (696ms)
		LoadAssemblies (569ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (244ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (211ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (856ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (634ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (132ms)
			ProcessInitializeOnLoadAttributes (416ms)
			ProcessInitializeOnLoadMethodAttributes (70ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 3.09 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5972 unused Assets / (7.1 MB). Loaded Objects now: 6693.
Memory consumption went from 230.3 MB to 223.2 MB.
Total: 31.435300 ms (FindLiveObjects: 2.910700 ms CreateObjectMapping: 3.234400 ms MarkObjects: 13.389000 ms  DeleteObjects: 11.897900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.877 seconds
Refreshing native plugins compatible for Editor in 1.52 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.827 seconds
Domain Reload Profiling: 1701ms
	BeginReloadAssembly (233ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (52ms)
	RebuildCommonClasses (42ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (42ms)
	LoadAllAssembliesAndSetupDomain (542ms)
		LoadAssemblies (469ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (199ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (175ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (827ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (617ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (124ms)
			ProcessInitializeOnLoadAttributes (399ms)
			ProcessInitializeOnLoadMethodAttributes (77ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 3.16 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5972 unused Assets / (7.3 MB). Loaded Objects now: 6695.
Memory consumption went from 230.3 MB to 223.0 MB.
Total: 30.761300 ms (FindLiveObjects: 1.819200 ms CreateObjectMapping: 3.154800 ms MarkObjects: 13.771800 ms  DeleteObjects: 12.011200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.812 seconds
Refreshing native plugins compatible for Editor in 1.50 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.875 seconds
Domain Reload Profiling: 1687ms
	BeginReloadAssembly (211ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (55ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (33ms)
	LoadAllAssembliesAndSetupDomain (516ms)
		LoadAssemblies (414ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (204ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (178ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (876ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (653ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (138ms)
			ProcessInitializeOnLoadAttributes (418ms)
			ProcessInitializeOnLoadMethodAttributes (78ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 3.12 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5972 unused Assets / (7.4 MB). Loaded Objects now: 6697.
Memory consumption went from 230.3 MB to 222.9 MB.
Total: 29.561700 ms (FindLiveObjects: 2.392800 ms CreateObjectMapping: 2.710700 ms MarkObjects: 12.610000 ms  DeleteObjects: 11.844600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 262.502563 seconds.
  path: Assets/Guard.prefab
  artifactKey: Guid(88688a3796f914344af0c37274c108da) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Guard.prefab using Guid(88688a3796f914344af0c37274c108da) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)The same field name is serialized multiple times in the class or its parent class. This is not supported: Base(MonoBehaviour) navAgent
 -> (artifact id: 'e8b3d9f7db60529a24807734983d519d') in 0.5657904 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 57

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.97 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5966 unused Assets / (7.4 MB). Loaded Objects now: 6839.
Memory consumption went from 238.4 MB to 231.0 MB.
Total: 36.711500 ms (FindLiveObjects: 2.078700 ms CreateObjectMapping: 2.794400 ms MarkObjects: 18.305500 ms  DeleteObjects: 13.530000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.867 seconds
Refreshing native plugins compatible for Editor in 1.61 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.819 seconds
Domain Reload Profiling: 1684ms
	BeginReloadAssembly (245ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (21ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (66ms)
	RebuildCommonClasses (43ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (526ms)
		LoadAssemblies (425ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (212ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (187ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (820ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (603ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (135ms)
			ProcessInitializeOnLoadAttributes (392ms)
			ProcessInitializeOnLoadMethodAttributes (59ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 3.14 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5972 unused Assets / (7.6 MB). Loaded Objects now: 6720.
Memory consumption went from 234.6 MB to 227.1 MB.
Total: 31.163600 ms (FindLiveObjects: 2.092800 ms CreateObjectMapping: 3.152900 ms MarkObjects: 13.294800 ms  DeleteObjects: 12.617300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.789 seconds
Refreshing native plugins compatible for Editor in 2.10 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.785 seconds
Domain Reload Profiling: 1573ms
	BeginReloadAssembly (210ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (52ms)
	RebuildCommonClasses (43ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (489ms)
		LoadAssemblies (402ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (190ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (167ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (786ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (575ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (122ms)
			ProcessInitializeOnLoadAttributes (374ms)
			ProcessInitializeOnLoadMethodAttributes (63ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 3.36 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5972 unused Assets / (7.7 MB). Loaded Objects now: 6722.
Memory consumption went from 234.5 MB to 226.8 MB.
Total: 32.777900 ms (FindLiveObjects: 2.426400 ms CreateObjectMapping: 3.258000 ms MarkObjects: 13.389600 ms  DeleteObjects: 13.700800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.96 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5966 unused Assets / (7.5 MB). Loaded Objects now: 6723.
Memory consumption went from 234.7 MB to 227.2 MB.
Total: 28.610000 ms (FindLiveObjects: 1.986800 ms CreateObjectMapping: 2.616700 ms MarkObjects: 12.113200 ms  DeleteObjects: 11.887200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 369.023958 seconds.
  path: Assets/Guard.prefab
  artifactKey: Guid(88688a3796f914344af0c37274c108da) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Guard.prefab using Guid(88688a3796f914344af0c37274c108da) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '572c052b69ead47196b550e21940aad4') in 0.6743817 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 57

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.829 seconds
Refreshing native plugins compatible for Editor in 1.65 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.831 seconds
Domain Reload Profiling: 1658ms
	BeginReloadAssembly (240ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (21ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (70ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (499ms)
		LoadAssemblies (409ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (190ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (164ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (831ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (608ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (124ms)
			ProcessInitializeOnLoadAttributes (403ms)
			ProcessInitializeOnLoadMethodAttributes (64ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 3.75 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5973 unused Assets / (7.6 MB). Loaded Objects now: 6745.
Memory consumption went from 238.9 MB to 231.3 MB.
Total: 28.915300 ms (FindLiveObjects: 2.294200 ms CreateObjectMapping: 2.383200 ms MarkObjects: 12.118800 ms  DeleteObjects: 12.115700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.18 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5966 unused Assets / (7.6 MB). Loaded Objects now: 6745.
Memory consumption went from 238.8 MB to 231.3 MB.
Total: 34.779300 ms (FindLiveObjects: 2.116900 ms CreateObjectMapping: 2.731900 ms MarkObjects: 15.957600 ms  DeleteObjects: 13.966600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 548.039468 seconds.
  path: Assets/Death Effect.prefab
  artifactKey: Guid(b960c0ed842bd6140acb83c1da5197e8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Death Effect.prefab using Guid(b960c0ed842bd6140acb83c1da5197e8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7479c45ca34240871a6a89b0e7548e83') in 0.3743588 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 15

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/TextMesh Pro/Resources/Fonts & Materials/LiberationSans SDF - Drop Shadow.mat
  artifactKey: Guid(e73a58f6e2794ae7b1b7e50b7fb811b0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Resources/Fonts & Materials/LiberationSans SDF - Drop Shadow.mat using Guid(e73a58f6e2794ae7b1b7e50b7fb811b0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2d8e5be150668872024fb79a4605ea20') in 0.0709738 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/TextMesh Pro/Resources/LineBreaking Leading Characters.txt
  artifactKey: Guid(d82c1b31c7e74239bff1220585707d2b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Resources/LineBreaking Leading Characters.txt using Guid(d82c1b31c7e74239bff1220585707d2b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4025504d821dd4e65f160aa09d1d4c27') in 0.0243125 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/TextMesh Pro/Sprites/EmojiOne.json
  artifactKey: Guid(8f05276190cf498a8153f6cbe761d4e6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Sprites/EmojiOne.json using Guid(8f05276190cf498a8153f6cbe761d4e6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '731df25519c21c141b525734cd1d36fa') in 0.0356935 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/TextMesh Pro/Shaders/TMP_SDF Overlay.shader
  artifactKey: Guid(dd89cf5b9246416f84610a006f916af7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Shaders/TMP_SDF Overlay.shader using Guid(dd89cf5b9246416f84610a006f916af7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c3a007b7af1fbd9a53c62b4b0f4be1ea') in 0.0279393 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/TextMesh Pro/Shaders/TMPro.cginc
  artifactKey: Guid(407bc68d299748449bbf7f48ee690f8d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Shaders/TMPro.cginc using Guid(407bc68d299748449bbf7f48ee690f8d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9a720aedd2963942491127fa3f954d39') in 0.0367672 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/TextMesh Pro/Shaders/TMP_SDF SSD.shader
  artifactKey: Guid(14eb328de4b8eb245bb7cea29e4ac00b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Shaders/TMP_SDF SSD.shader using Guid(14eb328de4b8eb245bb7cea29e4ac00b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd94e5d8e17b7c8448062b8fc206aec4d') in 0.0243799 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Guard.prefab
  artifactKey: Guid(88688a3796f914344af0c37274c108da) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Guard.prefab using Guid(88688a3796f914344af0c37274c108da) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd9f5747bab03d275256a7e9f3b2b0d73') in 0.0403407 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 41

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/TextMesh Pro/Shaders/TMP_SDF-Mobile SSD.shader
  artifactKey: Guid(c8d12adcee749c344b8117cf7c7eb912) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Shaders/TMP_SDF-Mobile SSD.shader using Guid(c8d12adcee749c344b8117cf7c7eb912) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fc5f456039d68de3e49c556410b8b128') in 0.0265997 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Button.cs
  artifactKey: Guid(27d4bc643b7b53a40a4cde96962fba00) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Button.cs using Guid(27d4bc643b7b53a40a4cde96962fba00) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5c5bc7b4de3e9c01054ad66ea14f24ba') in 0.0168243 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/TextMesh Pro/Shaders/SDFFunctions.hlsl
  artifactKey: Guid(96de908384869cd409c75efa351d5edf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Shaders/SDFFunctions.hlsl using Guid(96de908384869cd409c75efa351d5edf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a821f149dcd9f8e24e9719b579749f26') in 0.0282068 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/TextMesh Pro/Resources/Fonts & Materials/LiberationSans SDF - Fallback.asset
  artifactKey: Guid(2e498d1c8094910479dc3e1b768306a4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Resources/Fonts & Materials/LiberationSans SDF - Fallback.asset using Guid(2e498d1c8094910479dc3e1b768306a4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '798f2fad85bd25ca9e592b152947468b') in 0.0679209 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/TextMesh Pro/Sprites/EmojiOne.png
  artifactKey: Guid(dffef66376be4fa480fb02b19edbe903) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Sprites/EmojiOne.png using Guid(dffef66376be4fa480fb02b19edbe903) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '17448f0d6c1949097a267aabaac7c65e') in 0.0553914 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/WeaponBehavior.cs
  artifactKey: Guid(ffd8e305ddaf23e4dab7c418453d4ff2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/WeaponBehavior.cs using Guid(ffd8e305ddaf23e4dab7c418453d4ff2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '412009bd176f4e81d2729a9637cb4e91') in 0.0204449 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Railgun bullet 1.prefab
  artifactKey: Guid(00e61838a9626fc44a240adbe95ace29) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Railgun bullet 1.prefab using Guid(00e61838a9626fc44a240adbe95ace29) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '821fb0a59f61afbd4e43edb14a77f8c5') in 0.0639919 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/TextMesh Pro/Shaders/TMP_Bitmap-Custom-Atlas.shader
  artifactKey: Guid(48bb5f55d8670e349b6e614913f9d910) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Shaders/TMP_Bitmap-Custom-Atlas.shader using Guid(48bb5f55d8670e349b6e614913f9d910) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd65a6ce0e051afe51cacc044257bfe75') in 0.0202665 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/button.prefab
  artifactKey: Guid(aabf21289309a2647bcb735b4cf611e6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/button.prefab using Guid(aabf21289309a2647bcb735b4cf611e6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c08dde9a7c7bd490cbe0394585b28a95') in 0.0891792 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 21

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/TextMesh Pro/Shaders/TMP_Bitmap-Mobile.shader
  artifactKey: Guid(1e3b057af24249748ff873be7fafee47) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Shaders/TMP_Bitmap-Mobile.shader using Guid(1e3b057af24249748ff873be7fafee47) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '48290419b9242ae3ffe01fb72bf213e7') in 0.0187265 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/TextMesh Pro/Shaders
  artifactKey: Guid(e9f693669af91aa45ad615fc681ed29f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Shaders using Guid(e9f693669af91aa45ad615fc681ed29f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2c93efda4799af976ea6e766f4f2ea3e') in 0.0166409 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Partical Material.mat
  artifactKey: Guid(f5bd017c44e15d343b2385e35665eefa) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Partical Material.mat using Guid(f5bd017c44e15d343b2385e35665eefa) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '16c91e12c0e9775d7765cc9380071647') in 0.0231338 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/TextMesh Pro/Resources/Fonts & Materials
  artifactKey: Guid(731f1baa9d144a9897cb1d341c2092b8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Resources/Fonts & Materials using Guid(731f1baa9d144a9897cb1d341c2092b8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7e16e6d0532c4350dbfc075100d5c661') in 0.0138888 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/TextMesh Pro/Resources/Sprite Assets/EmojiOne.asset
  artifactKey: Guid(c41005c129ba4d66911b75229fd70b45) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Resources/Sprite Assets/EmojiOne.asset using Guid(c41005c129ba4d66911b75229fd70b45) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '21eccb8832df581e4fea33452433740e') in 0.0216478 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/TextMesh Pro/Shaders/TMP_SDF-URP Lit.shadergraph
  artifactKey: Guid(a3d800b099a06e0478fb790c5e79057a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Shaders/TMP_SDF-URP Lit.shadergraph using Guid(a3d800b099a06e0478fb790c5e79057a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '405284459fd348339b29a8d4afa39565') in 0.4796808 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/References.cs
  artifactKey: Guid(8e3d4ea909b18c44baf87fb9983b1664) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/References.cs using Guid(8e3d4ea909b18c44baf87fb9983b1664) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f64c5484ddf5d8f676bac3303f75c1ac') in 0.016689 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/TextMesh Pro/Shaders/TMP_SDF-Mobile-2-Pass.shader
  artifactKey: Guid(0178fcb869bafef4690d177d31d17db8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Shaders/TMP_SDF-Mobile-2-Pass.shader using Guid(0178fcb869bafef4690d177d31d17db8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '54448adbaee8147d6c188f4f4ba91626') in 0.0170891 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/weapon.prefab
  artifactKey: Guid(210d4db1fd7ebf84da01f2c4fd71ddea) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/weapon.prefab using Guid(210d4db1fd7ebf84da01f2c4fd71ddea) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c55fd2f685afefa35b074f4cdcea874f') in 0.0419856 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 22

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/TextMesh Pro/Shaders/TMP_SDF-HDRP LIT.shadergraph
  artifactKey: Guid(ca2ed216f98028c4dae6c5224a952b3c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Shaders/TMP_SDF-HDRP LIT.shadergraph using Guid(ca2ed216f98028c4dae6c5224a952b3c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '97eca93620d353e9a7f1fd3aefde3fa3') in 0.0354413 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Enemy.prefab
  artifactKey: Guid(6349f852420cc744b9790061728ac5c2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Enemy.prefab using Guid(6349f852420cc744b9790061728ac5c2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '95ae5edfa1fa8fc3e0c2c413af9d7bb5') in 0.0344672 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 36

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/navPoint.cs
  artifactKey: Guid(0dd3f646b313746499e2ea239b524f54) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/navPoint.cs using Guid(0dd3f646b313746499e2ea239b524f54) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fc0fbb7599957da1015fb5d5e06a3420') in 0.0203538 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/TextMesh Pro/Fonts/LiberationSans - OFL.txt
  artifactKey: Guid(6e59c59b81ab47f9b6ec5781fa725d2c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Fonts/LiberationSans - OFL.txt using Guid(6e59c59b81ab47f9b6ec5781fa725d2c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '154c1c8ff6f3439162a8a7673a9bac18') in 0.0294818 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/TextMesh Pro/Shaders/TMP_SDF-Mobile Masking.shader
  artifactKey: Guid(bc1ede39bf3643ee8e493720e4259791) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Shaders/TMP_SDF-Mobile Masking.shader using Guid(bc1ede39bf3643ee8e493720e4259791) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '92eb1b86f97725180c68e664773b44e8') in 0.0246472 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/TextMesh Pro/Shaders/TMP_SDF.shader
  artifactKey: Guid(68e6db2ebdc24f95958faec2be5558d6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Shaders/TMP_SDF.shader using Guid(68e6db2ebdc24f95958faec2be5558d6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6dc3e3affa0277fb1347c65a0e26f56a') in 0.0167469 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/TextMesh Pro/Sprites/EmojiOne Attribution.txt
  artifactKey: Guid(381dcb09d5029d14897e55f98031fca5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Sprites/EmojiOne Attribution.txt using Guid(381dcb09d5029d14897e55f98031fca5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ed6a01c5844242c4330a27b4aa9f0c5e') in 0.0215024 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/TextMesh Pro/Fonts/LiberationSans.ttf
  artifactKey: Guid(e3265ab4bf004d28a9537516768c1c75) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Fonts/LiberationSans.ttf using Guid(e3265ab4bf004d28a9537516768c1c75) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5532870d0bbdbc4f85ccf082adf10621') in 0.0280517 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/TextMesh Pro/Resources/Style Sheets
  artifactKey: Guid(4aecb92fff08436c8303b10eab8da368) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Resources/Style Sheets using Guid(4aecb92fff08436c8303b10eab8da368) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3070099268825bb3ad0df68e79183fa5') in 0.0138069 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.95 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5966 unused Assets / (8.0 MB). Loaded Objects now: 6749.
Memory consumption went from 240.9 MB to 232.9 MB.
Total: 32.757800 ms (FindLiveObjects: 2.321700 ms CreateObjectMapping: 2.967500 ms MarkObjects: 13.968300 ms  DeleteObjects: 13.496400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 259.736046 seconds.
  path: Assets/navPoint.cs
  artifactKey: Guid(0dd3f646b313746499e2ea239b524f54) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/navPoint.cs using Guid(0dd3f646b313746499e2ea239b524f54) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4e956731646f2170b845683781ad99e1') in 0.0379616 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Guard_Behaviour.cs
  artifactKey: Guid(515925c7523fec14aae9ec7f0e622c61) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Guard_Behaviour.cs using Guid(515925c7523fec14aae9ec7f0e622c61) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a5e87a49851b93215ff78768c031f6ba') in 0.0235957 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.37 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5966 unused Assets / (7.7 MB). Loaded Objects now: 6749.
Memory consumption went from 240.7 MB to 233.0 MB.
Total: 35.598300 ms (FindLiveObjects: 2.246700 ms CreateObjectMapping: 2.874000 ms MarkObjects: 16.507800 ms  DeleteObjects: 13.966000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 339.368744 seconds.
  path: Assets/Guard_Behaviour.cs
  artifactKey: Guid(515925c7523fec14aae9ec7f0e622c61) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Guard_Behaviour.cs using Guid(515925c7523fec14aae9ec7f0e622c61) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '84e41968818b99758d567a0fbc1bc3ec') in 0.02238 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 5.564862 seconds.
  path: Assets/Nav Point.prefab
  artifactKey: Guid(908f43f7e0323e345b39ba7f4042955d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nav Point.prefab using Guid(908f43f7e0323e345b39ba7f4042955d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '14a7e16b820bb2726c4191ef47125971') in 0.4889809 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.435275 seconds.
  path: Assets/Nav Point.prefab
  artifactKey: Guid(908f43f7e0323e345b39ba7f4042955d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nav Point.prefab using Guid(908f43f7e0323e345b39ba7f4042955d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b06a03afe2796c27f31dbccdef913d45') in 0.0179419 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 61.234351 seconds.
  path: Assets/asf.unity
  artifactKey: Guid(58219c39114fbcd4f87a63ec331b9b69) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/asf.unity using Guid(58219c39114fbcd4f87a63ec331b9b69) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8d4c201d244113a96bbfd633f455a08e') in 0.016684 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

