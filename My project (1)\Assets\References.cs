using UnityEngine;
using System.Collections.Generic;

public static class References
{
    public static GameObject The_Player;
    public static GameObject Canvas;
    public static Enemy_Spawner spawner;
    public static List<NavPoint> navPoint = new List<NavPoint>();
    public static float maxDistanceInALevel = 1000;
    public static LayerMask enemiesLayer = LayerMask.GetMask("enemies");
    public static LayerMask wallsLayer = LayerMask.GetMask("Walls");
    public static screenshake screenshake;
}
