Using pre-set license
Built from '6000.0/release' branch; Version is '6000.0.45f1 (d91bd3d4e081) revision 14228435'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 15790 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Users\<USER>\Documents\6000.0.45f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker3
-projectPath
C:/Users/<USER>/My project (1)
-logFile
Logs/AssetImportWorker3.log
-srvPort
50183
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: C:/Users/<USER>/My project (1)
C:/Users/<USER>/My project (1)
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [6272]  Target information:

Player connection [6272]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 593677054 [EditorId] 593677054 [Version] 1048832 [Id] WindowsEditor(7,V) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [6272] Host joined multi-casting on [***********:54997]...
Player connection [6272] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 3.50 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.45f1 (d91bd3d4e081)
[Subsystems] Discovering subsystems at path C:/Users/<USER>/Documents/6000.0.45f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/My project (1)/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 3050 Laptop GPU (ID=0x25a2)
    Vendor:   NVIDIA
    VRAM:     3964 MB
    Driver:   32.0.15.7640
Initialize mono
Mono path[0] = 'C:/Users/<USER>/Documents/6000.0.45f1/Editor/Data/Managed'
Mono path[1] = 'C:/Users/<USER>/Documents/6000.0.45f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Users/<USER>/Documents/6000.0.45f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56932
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Users/<USER>/Documents/6000.0.45f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.003617 seconds.
- Loaded All Assemblies, in  0.572 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.468 seconds
Domain Reload Profiling: 1037ms
	BeginReloadAssembly (213ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (56ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (70ms)
	LoadAllAssembliesAndSetupDomain (214ms)
		LoadAssemblies (207ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (210ms)
			TypeCache.Refresh (208ms)
				TypeCache.ScanAssembly (189ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (469ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (407ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (36ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (78ms)
			ProcessInitializeOnLoadAttributes (219ms)
			ProcessInitializeOnLoadMethodAttributes (68ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.081 seconds
Refreshing native plugins compatible for Editor in 1.46 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.853 seconds
Domain Reload Profiling: 1927ms
	BeginReloadAssembly (241ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (49ms)
	RebuildCommonClasses (48ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (52ms)
	LoadAllAssembliesAndSetupDomain (719ms)
		LoadAssemblies (547ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (312ms)
			TypeCache.Refresh (243ms)
				TypeCache.ScanAssembly (223ms)
			BuildScriptInfoCaches (54ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (854ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (641ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (116ms)
			ProcessInitializeOnLoadAttributes (404ms)
			ProcessInitializeOnLoadMethodAttributes (106ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 3.02 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 212 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5976 unused Assets / (7.5 MB). Loaded Objects now: 6639.
Memory consumption went from 237.0 MB to 229.5 MB.
Total: 30.828800 ms (FindLiveObjects: 2.263700 ms CreateObjectMapping: 4.161800 ms MarkObjects: 12.837400 ms  DeleteObjects: 11.561000 ms)

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0