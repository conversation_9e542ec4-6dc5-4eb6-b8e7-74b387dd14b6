using UnityEngine;
using UnityEngine.AI;

public class Enemy_Behaviour : MonoBeh<PERSON>our
{
    public float speed;
    public Rigidbody rb;
    public NavMeshAgent navAgent ;

    protected virtual void Start()
    {
        rb = GetComponent<Rigidbody>();
        navAgent = GetComponent<NavMeshAgent>();
        navAgent.speed = speed;
    }

    // Update is called once per frame
    protected virtual void Update()
    {
        ChasePlayer();
    }
    protected void ChasePlayer()
    {
        if (References.The_Player != null)
        {
            navAgent.destination = References.The_Player.transform.position;

            /*
            Vector3 PlayerPosition = References.The_Player.transform.position;
            Vector3 VectorToPlayer = PlayerPosition - transform.position;
            rb.linearVelocity = VectorToPlayer.normalized * speed;
            Vector3 PlayerPositionAtOurHeight = new Vector3(PlayerPosition.x, transform.position.y, PlayerPosition.z);
            transform.LookAt(PlayerPositionAtOurHeight); */
        }
    }
    protected void OnCollisionEnter(Collision this_Collision)
    {
        GameObject OB = this_Collision.gameObject;  
        
        if (OB.GetComponent<Player_Behaviour>() != null)
        {
            HealthSystem HS = OB.GetComponent<HealthSystem>();
            if (HS != null)
            {   
                HS.TakeDamage(1);
            }
        }
    }
}
