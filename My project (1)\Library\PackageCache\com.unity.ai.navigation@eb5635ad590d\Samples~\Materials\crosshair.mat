%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: crosshair
  m_Shader: {fileID: 10753, guid: 0000000000000000e000000000000000, type: 0}
  m_ShaderKeywords: _ALPHATEST_ON
  m_LightmapFlags: 5
  m_CustomRenderQueue: 2450
  stringTagMap:
    RenderType: TransparentCutout
  m_SavedProperties:
    serializedVersion: 2
    m_TexEnvs:
      data:
        first:
          name: _MainTex
        second:
          m_Texture: {fileID: 2800000, guid: 28d2c2677149d45e290e8fa042b1b80f, type: 3}
          m_Scale: {x: 1, y: 1}
          m_Offset: {x: 0, y: 0}
      data:
        first:
          name: _BumpMap
        second:
          m_Texture: {fileID: 0}
          m_Scale: {x: 1, y: 1}
          m_Offset: {x: 0, y: 0}
      data:
        first:
          name: _DetailNormalMap
        second:
          m_Texture: {fileID: 0}
          m_Scale: {x: 1, y: 1}
          m_Offset: {x: 0, y: 0}
      data:
        first:
          name: _ParallaxMap
        second:
          m_Texture: {fileID: 0}
          m_Scale: {x: 1, y: 1}
          m_Offset: {x: 0, y: 0}
      data:
        first:
          name: _OcclusionMap
        second:
          m_Texture: {fileID: 0}
          m_Scale: {x: 1, y: 1}
          m_Offset: {x: 0, y: 0}
      data:
        first:
          name: _EmissionMap
        second:
          m_Texture: {fileID: 0}
          m_Scale: {x: 1, y: 1}
          m_Offset: {x: 0, y: 0}
      data:
        first:
          name: _DetailMask
        second:
          m_Texture: {fileID: 0}
          m_Scale: {x: 1, y: 1}
          m_Offset: {x: 0, y: 0}
      data:
        first:
          name: _DetailAlbedoMap
        second:
          m_Texture: {fileID: 0}
          m_Scale: {x: 1, y: 1}
          m_Offset: {x: 0, y: 0}
      data:
        first:
          name: _MetallicGlossMap
        second:
          m_Texture: {fileID: 0}
          m_Scale: {x: 1, y: 1}
          m_Offset: {x: 0, y: 0}
      data:
        first:
          name: _DetailTex
        second:
          m_Texture: {fileID: 0}
          m_Scale: {x: 1, y: 1}
          m_Offset: {x: 0, y: 0}
    m_Floats:
      data:
        first:
          name: _SrcBlend
        second: 1
      data:
        first:
          name: _DstBlend
        second: 0
      data:
        first:
          name: _Cutoff
        second: .66900003
      data:
        first:
          name: PixelSnap
        second: 0
      data:
        first:
          name: _Parallax
        second: .0199999996
      data:
        first:
          name: _ZWrite
        second: 1
      data:
        first:
          name: _Glossiness
        second: .904999971
      data:
        first:
          name: _BumpScale
        second: 1
      data:
        first:
          name: _OcclusionStrength
        second: 1
      data:
        first:
          name: _DetailNormalMapScale
        second: 1
      data:
        first:
          name: _UVSec
        second: 0
      data:
        first:
          name: _Mode
        second: 1
      data:
        first:
          name: _Metallic
        second: 0
      data:
        first:
          name: _Stencil
        second: 0
      data:
        first:
          name: _StencilComp
        second: 255
      data:
        first:
          name: _StencilOp
        second: 0
      data:
        first:
          name: _StencilReadMask
        second: 255
      data:
        first:
          name: _StencilWriteMask
        second: 255
      data:
        first:
          name: _ColorMask
        second: 255
      data:
        first:
          name: _Strength
        second: .246000007
    m_Colors:
      data:
        first:
          name: _EmissionColor
        second: {r: 0, g: 0, b: 0, a: 1}
      data:
        first:
          name: _Color
        second: {r: 1, g: 1, b: 1, a: 1}
