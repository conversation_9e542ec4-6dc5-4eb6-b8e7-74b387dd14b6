Using pre-set license
Built from '6000.0/release' branch; Version is '6000.0.45f1 (d91bd3d4e081) revision 14228435'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 15790 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Users\<USER>\Documents\6000.0.45f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
C:/Users/<USER>/My project (1)
-logFile
Logs/AssetImportWorker0.log
-srvPort
50262
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: C:/Users/<USER>/My project (1)
C:/Users/<USER>/My project (1)
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [18524]  Target information:

Player connection [18524]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 1920220036 [EditorId] 1920220036 [Version] 1048832 [Id] WindowsEditor(7,V) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [18524] Host joined multi-casting on [***********:54997]...
Player connection [18524] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 2.02 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.45f1 (d91bd3d4e081)
[Subsystems] Discovering subsystems at path C:/Users/<USER>/Documents/6000.0.45f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/My project (1)/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 3050 Laptop GPU (ID=0x25a2)
    Vendor:   NVIDIA
    VRAM:     3964 MB
    Driver:   32.0.15.7640
Initialize mono
Mono path[0] = 'C:/Users/<USER>/Documents/6000.0.45f1/Editor/Data/Managed'
Mono path[1] = 'C:/Users/<USER>/Documents/6000.0.45f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Users/<USER>/Documents/6000.0.45f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56436
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Users/<USER>/Documents/6000.0.45f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.003310 seconds.
- Loaded All Assemblies, in  0.453 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.402 seconds
Domain Reload Profiling: 853ms
	BeginReloadAssembly (160ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (41ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (57ms)
	LoadAllAssembliesAndSetupDomain (179ms)
		LoadAssemblies (155ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (176ms)
			TypeCache.Refresh (174ms)
				TypeCache.ScanAssembly (160ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (403ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (351ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (28ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (62ms)
			ProcessInitializeOnLoadAttributes (190ms)
			ProcessInitializeOnLoadMethodAttributes (66ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.073 seconds
Refreshing native plugins compatible for Editor in 1.46 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.936 seconds
Domain Reload Profiling: 2003ms
	BeginReloadAssembly (233ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (58ms)
	RebuildCommonClasses (43ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (732ms)
		LoadAssemblies (478ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (377ms)
			TypeCache.Refresh (269ms)
				TypeCache.ScanAssembly (238ms)
			BuildScriptInfoCaches (87ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (937ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (729ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (132ms)
			ProcessInitializeOnLoadAttributes (456ms)
			ProcessInitializeOnLoadMethodAttributes (125ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 3.27 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 212 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5975 unused Assets / (7.8 MB). Loaded Objects now: 6638.
Memory consumption went from 233.3 MB to 225.5 MB.
Total: 29.998900 ms (FindLiveObjects: 2.001700 ms CreateObjectMapping: 3.581300 ms MarkObjects: 13.465200 ms  DeleteObjects: 10.946700 ms)

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.844 seconds
Refreshing native plugins compatible for Editor in 1.38 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.826 seconds
Domain Reload Profiling: 1669ms
	BeginReloadAssembly (233ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (63ms)
	RebuildCommonClasses (41ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (522ms)
		LoadAssemblies (416ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (217ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (188ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (827ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (597ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (118ms)
			ProcessInitializeOnLoadAttributes (397ms)
			ProcessInitializeOnLoadMethodAttributes (67ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 3.55 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 45 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5972 unused Assets / (6.5 MB). Loaded Objects now: 6654.
Memory consumption went from 218.7 MB to 212.3 MB.
Total: 40.429900 ms (FindLiveObjects: 6.670500 ms CreateObjectMapping: 3.297100 ms MarkObjects: 18.184700 ms  DeleteObjects: 12.273100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1369.079227 seconds.
  path: Assets/Enemy.prefab
  artifactKey: Guid(6349f852420cc744b9790061728ac5c2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Enemy.prefab using Guid(6349f852420cc744b9790061728ac5c2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '530fc19221c2676d30d560600225a745') in 0.6913995 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 38

========================================================================
Received Import Request.
  Time since last request: 13.994780 seconds.
  path: Assets/Guard.prefab
  artifactKey: Guid(88688a3796f914344af0c37274c108da) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Guard.prefab using Guid(88688a3796f914344af0c37274c108da) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e936dbafcb98d880d6c33a2597f8a6dd') in 0.0177409 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 56

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.030 seconds
Refreshing native plugins compatible for Editor in 1.78 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.880 seconds
Domain Reload Profiling: 1907ms
	BeginReloadAssembly (271ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (20ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (82ms)
	RebuildCommonClasses (41ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (40ms)
	LoadAllAssembliesAndSetupDomain (655ms)
		LoadAssemblies (507ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (264ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (230ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (880ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (631ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (129ms)
			ProcessInitializeOnLoadAttributes (422ms)
			ProcessInitializeOnLoadMethodAttributes (63ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 2.90 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5973 unused Assets / (5.4 MB). Loaded Objects now: 6692.
Memory consumption went from 230.5 MB to 225.0 MB.
Total: 46.916800 ms (FindLiveObjects: 2.066900 ms CreateObjectMapping: 3.363100 ms MarkObjects: 24.418300 ms  DeleteObjects: 17.064400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 86.522904 seconds.
  path: Assets/Enemy.prefab
  artifactKey: Guid(6349f852420cc744b9790061728ac5c2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Enemy.prefab using Guid(6349f852420cc744b9790061728ac5c2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8b84a9e6f5838d9ba072fb2f7c31099e') in 0.5921046 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 38

