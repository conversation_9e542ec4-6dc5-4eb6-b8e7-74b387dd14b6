using UnityEngine;
public class Guard_Behaviour : Enemy_Behaviour
{
    // No need to redeclare speed, Turn_Speed, or rb as they're inherited
    public float VisionRange;
    public float VisionConeAngle;
    public bool alerted;
    public Light myLight;
    public float Turn_Speed;

    protected override void Start()
    {
        base.Start();
        alerted = false;
        int randomNavPointIndex = Random.Range(0, References.navPoint.Count);
        Refences.destination = References.navPoint[0].transform.position;
    }
    
    protected override void Update()
    {
        if (References.The_Player != null)
        {
            Vector3 PlayerPosition = References.The_Player.transform.position;
            Vector3 VectorToPlayer = PlayerPosition - transform.position;
            myLight.color = Color.white;

            if (alerted)
            {
                myLight.color = Color.red;
                ChasePlayer();
            }
            else 
            {
                //rotate
                Vector3 LateralOffset = transform.right * Turn_Speed * Time.deltaTime;
                transform.LookAt(transform.position + transform.forward + LateralOffset);
                rb.linearVelocity = transform.forward * speed;

                //check for player
                if (Vector3.Distance(transform.position, PlayerPosition) <= VisionRange)
                {
                    if (Vector3.Angle(transform.forward, VectorToPlayer) <= VisionConeAngle)
                    { // (start,end,defined new variable to store info, where it go,object that intercepted it)
                        if (Physics.Raycast(transform.position, VectorToPlayer, VectorToPlayer.magnitude, References.wallsLayer) == false) {
                            // first time see player
                            alerted = true;
                            References.spawner.activated = true; 
                        }                                 
                    }
                }
            }
        }
    }    
}
